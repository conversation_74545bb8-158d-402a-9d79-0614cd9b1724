/*
 * This file was generated by the Gradle 'init' task.
 */

group = 'de.monticore.lang'
version = '7.8.0-SNAPSHOT'

apply plugin: 'java'
apply plugin: 'maven-publish'

dependencies {
  implementation group: 'de.monticore.lang', name: 'cd4analysis', version: mc_version
  implementation group: 'de.monticore', name: 'monticore-runtime', version: mc_version
  implementation group: 'de.monticore', name: 'monticore-grammar', version: mc_version
  implementation group: 'de.monticore', name: 'class2mc', version: mc_version
  implementation group: 'de.se_rwth.commons', name: 'se-commons-logging', version: mc_version

  testImplementation group: 'junit', name: 'junit', version: junit_version
  testImplementation group: 'ch.qos.logback', name: 'logback-classic', version: '1.4.5'
  testImplementation group: 'de.monticore.lang', name: 'guidsl', version: mc_version
}

sourceCompatibility = JavaVersion.VERSION_11

tasks.withType(JavaCompile).configureEach {
  options.encoding = 'UTF-8'
}

repositories {
  mavenLocal()
  maven {
    credentials.username mavenUser
    credentials.password mavenPassword
    url repo
  }
  mavenCentral()
}

// configure deployment
publishing {
  // configure what artifacts to publish
  publications {
    mavenJava(MavenPublication) {
      from(components.java)
    }
  }
  repositories.maven {
    credentials.username mavenUser
    credentials.password mavenPassword
    def releasesRepoUrl = "https://nexus.se.rwth-aachen.de/content/repositories/monticore-releases/"
    def snapshotsRepoUrl = "https://nexus.se.rwth-aachen.de/content/repositories/monticore-snapshots/"
    url = version.endsWith("SNAPSHOT") ? snapshotsRepoUrl : releasesRepoUrl
  }
}