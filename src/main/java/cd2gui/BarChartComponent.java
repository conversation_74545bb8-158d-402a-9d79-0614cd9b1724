public class BarChartComponent implements ChartComponent {
    private GemBarChartData data;
    private boolean stacked;
    private Integer maxValue;
    private Integer minValue;

    public BarChartComponent() {
        this.stacked = false;
        this.maxValue = null;
        this.minValue = null;
    }

    @Override
    public void setData(Object data) {
        if (!(data instanceof GemBarChartData)) {
            throw new IllegalArgumentException(
                    "BarChartComponent needs GemBarChartData as data!");
        }
        this.data = (GemBarChartData) data;
    }

    @Override
    public void setConfig(ChartConfig config) {
        this.stacked  = config.isStacked();
        this.maxValue = config.getMaxValue();
        this.minValue = config.getMinValue();
    }

    @Override
    public void render() {
        // 真正绘制柱状图的逻辑……
    }
}
