/* (c) https://github.com/MontiCore/monticore */
package cd2gui;

import cd2gui.data.CD2GUIClassTreeNode;
import cd2gui.util.*;
import com.google.common.collect.Lists;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.cdbasis._ast.ASTCDClassTOP;
import de.monticore.cdbasis._ast.ASTCDCompilationUnit;
import de.monticore.generating.GeneratorSetup;
import de.monticore.generating.templateengine.GlobalExtensionManagement;
import de.monticore.io.paths.MCPath;
import de.se_rwth.commons.Joiners;
import de.se_rwth.commons.Names;
import de.se_rwth.commons.logging.Log;

import cd2gui.metrics.*;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.HashSet;
import java.util.Set;


public class CD2GUITool {

  private final ASTCDCompilationUnit astCDCompUnit;

  private List<ASTCDClass> classes;

  private final File targetFilepath;

  private final MCPath hwcPath;

  private GuiModelFileCreator guiModelFileCreator;

  private String domainPackage;

  private List<CD2GUIClassTreeNode> classTrees;

  private static List<String> options;

  private final GeneratorSetup setup;

  private MetricManager metricManager;


  /**
   * CD2GUI allows the generation of gui-pages that can display, create and alter instances of classes in the classdiagram
   * @param domain The parsed domain file of the application
   * @param targetFilepath The Folder where the gui-models are generated into.
   * @param hwcPath The Path of handwritten files
   * @param options Options for generation, currently supported are "NoDashboard", "NoOverview", "NoDetails", "NoDetailsEdit", "NoForm"
   *                if these flags are set, the corresponding Page won't be generated.
   *                Additionally, "ExcludeForm", followed by  the domain class fqn is supported to exclude the generation of forms for specific classes
   */
  public CD2GUITool(ASTCDCompilationUnit domain, File targetFilepath, MCPath hwcPath, String... options){
    CD2GUITool.options = List.of(options);
    this.targetFilepath = targetFilepath;
    this.hwcPath = hwcPath;
    this.astCDCompUnit = domain;

    if(astCDCompUnit.isPresentMCPackageDeclaration()) {
      domainPackage = astCDCompUnit.getMCPackageDeclaration().getMCQualifiedName().toString() + "." + astCDCompUnit.getCDDefinition().getName();
    }
    else {
      domainPackage = astCDCompUnit.getCDDefinition().getName();
    }
    setup = new GeneratorSetup();
    setup.setOutputDirectory(targetFilepath);
    setup.setTracing(false);
    GlobalExtensionManagement glex = new GlobalExtensionManagement();
    glex.setGlobalValue("attrManager", new AttributeManager());
    glex.setGlobalValue("roleManager", new RoleManager());
    glex.setGlobalValue("nameTransformer", new NameTransformer());
    glex.setGlobalValue("linkPath", Names.getPathFromQualifiedName(
            Joiners.DOT.join(domainPackage, astCDCompUnit.getCDDefinition().getName())).toLowerCase());
    setup.setGlex(glex);

    this.metricManager = MetricManager.getInstance();
  }

  /**
   * CD2GUI allows the generation of gui-pages that can display, create and alter instances of classes in the classdiagram
   * @param domain The parsed domain file of the application
   * @param targetFilepath The Folder where the gui-models are generated into.
   * @param hwcPath The Path of handwritten files
   * @param templatePath alternate Locations where the generator will look for templates this simplifies the template replacement
   * @param options Options for generation, currently supported are "NoDashboard", "NoOverview", "NoDetails", "NoDetailsEdit", "NoForm"
   *                if these flags are set, the corresponding Page won't be generated.
   */
  public CD2GUITool(ASTCDCompilationUnit domain, File targetFilepath, MCPath hwcPath, File templatePath, String... options){
    this(domain, targetFilepath, hwcPath, options);

    setup.setAdditionalTemplatePaths(Lists.newArrayList(templatePath));

    this.metricManager = MetricManager.getInstance();

  }

  public void generateGUI() {

    Log.info("       .oooooo.   oooooooooo.     .oooo.     .oooooo.    ooooo     ooo ooooo \n" +
            "      d8P'  `Y8b  `888'   `Y8b  .dP\"\"Y88b   d8P'  `Y8b   `888'     `8' `888' \n" +
            "     888           888      888       ]8P' 888            888       8   888  \n" +
            "     888           888      888     .d8P'  888            888       8   888  \n" +
            "     888           888      888   .dP'     888     ooooo  888       8   888  \n" +
            "     `88b    ooo   888     d88' .oP     .o `88.    .88'   `88.    .8'   888  \n" +
            "      `Y8bood8P'  o888bood8P'   8888888888  `Y8bood8P'      `YbodP'    o888o ", "cd2gui");

    Log.debug("--------------------------------", "cd2gui");
    Log.debug( "\n" +
                    "Output dir     : " + targetFilepath + "\n" +
                    "HWC dir        : " + hwcPath,
            "cd2gui"
    );
    Log.info("--------------------------------", "cd2gui");

    generateAST();

    // 初始化指标系统 - 在AST生成后调用
    initializeMetricSystem();

    // 将指标管理器传递给文件生成器
    if (guiModelFileCreator != null) {
      guiModelFileCreator.setMetricManager(metricManager);
    }

    classTrees = ASTCDClassManager.buildSubclassTrees(astCDCompUnit.getCDDefinition().getCDClassesList());

    // -----------------------------------------------
    // Create GUI Model Files
    // -----------------------------------------------

    Log.info("creating gui-models", "cd2gui");

    guiModelFileCreator = new GuiModelFileCreator(classes, classTrees, setup, astCDCompUnit.getCDDefinition().getName(), domainPackage);

    try {
      if(!options.contains("NoDashboard"))
        guiModelFileCreator.createDashboard();
      if(!options.contains("NoOverview"))
        guiModelFileCreator.createOverviewPages();
      if(!options.contains("NoDetails"))
        guiModelFileCreator.createDetailsPages();
      if(!options.contains("NoDetailsEdit"))
        guiModelFileCreator.createDetailsEditPages();
      if(!options.contains("NoForm")) {
        List<String> excludedForms = new ArrayList<>();
        String last = null;
        for (String o : options) {
          if("ExcludeForm".equals(last)){
            excludedForms.add(o);
          }

          last = o;
        }
        guiModelFileCreator.createFormPages(excludedForms);
      }
    }
    catch (Exception e) {
      Log.error("CD2GUI0x200 writing a gui file fails. " + e.getMessage());
    }
  }

  /**
   * Generating the ASTs for the domain classdiagram and setting up the package for DataClass imports
   */
  public void generateAST(){

    classes = astCDCompUnit.getCDDefinition().getCDClassesList().stream()
                .filter(ASTCDClassManager::hasPage)
                .sorted(Comparator.comparing(ASTCDClassTOP::getName))
                .collect(Collectors.toList());

    Types.setClassesWithDetailsPage(classes.stream().map(ASTCDClass::getName).collect(Collectors.toList()));

    //the same for now, should change when abstract classes have an overview page
    Types.setClassesWithOverviewPage(classes.stream().map(ASTCDClass::getName).collect(Collectors.toList()));

    Log.info("cd Loaded", "CD2GUITool");

  }

  private void initializeMetricSystem() {
    Log.info("Initializing metric system...", "CD2GUITool");

    // 清除之前的指标记录
    metricManager.clearMetrics();

    // 从类图中识别指标
    identifyMetricsFromClassDiagram();

    // 注册一些默认/示例指标（在实际项目中可能会从配置文件或注解中读取）
    registerDefaultMetrics();

    Log.info("Metric system initialized with " + metricManager.getAllMetrics().size() + " metrics", "CD2GUITool");
  }

  private void identifyMetricsFromClassDiagram() {
    if (classes == null || classes.isEmpty()) {
      Log.warn("No classes available for metric identification", "CD2GUITool");
      return;
    }

    StereotypeManager stereotypeManager = new StereotypeManager();

    // 遍历所有类
    for (ASTCDClass cdClass : classes) {
      // 这里我们需要根据具体的AST结构获取类的属性
      // 下面是一个简化的示例，实际实现需要根据项目的AST结构进行调整
      cdClass.getCDAttributeList().forEach(attribute -> {
        // 检查属性是否有指标构造型
        if (stereotypeManager.hasMetricStereotype(attribute, MetricStereotype.METRIC)) {
          // 提取指标信息
          String name = attribute.getName();
          String description = stereotypeManager.getMetricStereotypeValue(attribute, MetricStereotype.METRIC);

          // 确定量表类型
          MetricScale scale = MetricScale.NOMINAL; // 默认值
          if (stereotypeManager.hasMetricStereotype(attribute, MetricStereotype.METRIC_SCALE)) {
            String scaleValue = stereotypeManager.getMetricStereotypeValue(attribute, MetricStereotype.METRIC_SCALE);
            try {
              scale = MetricScale.valueOf(scaleValue.toUpperCase());
            } catch (IllegalArgumentException e) {
              Log.warn("Invalid metric scale value: " + scaleValue, "CD2GUITool");
            }
          }

          // 确定图表类型
          Set<String> chartTypes = new HashSet<>();
          if (stereotypeManager.hasMetricStereotype(attribute, MetricStereotype.CHART_TYPE)) {
            String chartTypesStr = stereotypeManager.getMetricStereotypeValue(attribute, MetricStereotype.CHART_TYPE);
            for (String chartType : chartTypesStr.split(",")) {
              chartTypes.add(chartType.trim());
            }
          } else {
            // 默认图表类型
            chartTypes.add("bar");
          }

          // 确定可见性
          Set<String> visibilityContexts = new HashSet<>();
          if (stereotypeManager.hasMetricStereotype(attribute, MetricStereotype.METRIC_HIDDEN)) {
            // 如果有hidden构造型，指定可见性上下文
            String hiddenContextsStr = stereotypeManager.getMetricStereotypeValue(attribute, MetricStereotype.METRIC_HIDDEN);
            for (String context : hiddenContextsStr.split(",")) {
              visibilityContexts.add(context.trim());
            }
          } else {
            // 默认在所有上下文中可见
            visibilityContexts.add("dashboard");
            visibilityContexts.add("detail");
          }

          // 确定值类型（根据属性类型）
          Class<?> valueType = determineValueType(attribute.getType());

          // 创建指标对象
          CD2GUIMetric metric = new CD2GUIMetric(
                  name,
                  description,
                  scale,
                  valueType,
                  chartTypes,
                  visibilityContexts,
                  "attribute:" + cdClass.getName() + "." + name
          );

          // 注册指标
          metricManager.registerMetric(metric);

          Log.debug("Registered metric: " + name, "CD2GUITool");
        }
      });
    }
  }

  /**
   * 根据属性类型确定值类型
   */
  private Class<?> determineValueType(String typeString) {
    // 简化的类型映射，实际项目中可能需要更复杂的类型解析
    switch (typeString.toLowerCase()) {
      case "int":
      case "integer":
        return Integer.class;
      case "double":
      case "float":
        return Double.class;
      case "boolean":
        return Boolean.class;
      case "string":
        return String.class;
      default:
        // 对于其他类型，默认为Object
        return Object.class;
    }
  }

  /**
   * 注册默认指标（作为示例）
   */
  private void registerDefaultMetrics() {
    // 示例：注册一个系统级别的指标
    CD2GUIMetric classCountMetric = new CD2GUIMetric(
            "ClassCount",
            "Total number of classes in the model",
            MetricScale.RATIO,
            Integer.class,
            Set.of("bar", "gauge"),
            Set.of("dashboard"),
            "system:classCount"
    );
    metricManager.registerMetric(classCountMetric);

    // 示例：注册一个属性计数指标
    CD2GUIMetric attributeCountMetric = new CD2GUIMetric(
            "AttributeCount",
            "Average number of attributes per class",
            MetricScale.RATIO,
            Double.class,
            Set.of("bar", "line"),
            Set.of("dashboard"),
            "system:attributeCount"
    );
    metricManager.registerMetric(attributeCountMetric);
  }

  public MetricManager getMetricManager() {
    return metricManager;
  }

  public GuiModelFileCreator getGuiModelFileCreator() {
    //used only for Debugging or Tests
    return guiModelFileCreator;
  }

  public List<ASTCDClass> getClasses() {
    //used only for Debugging or Tests
    return classes;
  }

  public List<CD2GUIClassTreeNode> getClassTrees() {
    //used only for Debugging or Tests
    return classTrees;
  }

  public String getDomainPackage() {
    //used only for Debugging or Tests
    return domainPackage;
  }

  public void setGuiModelFileCreator(GuiModelFileCreator guiModelFileCreator) {
    //used only for Debugging or Tests
    this.guiModelFileCreator = guiModelFileCreator;
  }

  public static boolean isOption(String option){
    return CD2GUITool.options.contains(option);
  }
}
