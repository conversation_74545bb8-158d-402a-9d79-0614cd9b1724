/* (c) https://github.com/MontiCore/monticore */
package cd2gui.util;

import java.util.Arrays;


public class NameTransformer {

    /**
     * converts the string to the naming convention of .gui Files - all lower case and '-' between words
     * @param str the original string
     * @return the converted string
     */
    public static String insertMinus(String str){
        return String.join("-",
                Arrays.asList(str.split("(?=[A-Z])"))
        ).toLowerCase();
    }

    /**
     * returns the corresponding details page name to the overview page name
     * @param overview the overview page name
     * @return the corresponding details page
     */
    public static String getDetailsNavigationName(String overview){
        return overview.replace("-overview", "-details");
    }

    /**
     * returns the corresponding overview page name to the details page name
     * @param overview the details page name
     * @return the corresponding overview page
     */
    public static String getOverviewNavigationName(String overview){
        return overview.replace("-details", "-overview");
    }

  /**
   * returns the human name for an enum option
   * @param enumOption the enum option
   * @return the capitalized low case option string
   */
    public static String getEnumOptionHumanName(String enumOption) {
      if (enumOption.isEmpty()) {
        return enumOption;
      }
      return enumOption.substring(0, 1).toUpperCase().concat(enumOption.toLowerCase().substring(1));
    }
}
