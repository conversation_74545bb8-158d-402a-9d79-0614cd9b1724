/* (c) https://github.com/MontiCore/monticore */
package cd2gui.util;

import cd2gui.data.CD2GUIClassTreeNode;
import de.monticore.ast.ASTNode;
import de.monticore.cdassociation._symboltable.CDRoleSymbol;
import de.monticore.cdbasis._ast.ASTCDAttribute;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.cdinterfaceandenum._ast.ASTCDEnum;
import de.monticore.symbols.basicsymbols._symboltable.TypeSymbol;
import de.monticore.symbols.basicsymbols._symboltable.TypeSymbolTOP;
import de.monticore.types.check.SymTypeExpression;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class ASTCDClassManager {

  /**
   * Calculates all super classes by using the method of the symbol and casting them back to ASTCDClass
   * NOTE: This does only work iff all super classes are in the same CD.
   * Once symbols of super classes are imported from other cds,
   * the imported symbols might not have the corresponding ASTNode attached to them.
   *
   * @param clazz Class for which all super classes should be calculated.
   * @return List of all super classes.
   */
  public static List<ASTCDClass> getAllSuperClasses(ASTCDClass clazz) {
    return getAllSuperClasses(clazz.getSymbol()).stream()
        .map(TypeSymbolTOP::getAstNode)
        .filter(s -> s instanceof ASTCDClass)
        .map(s -> (ASTCDClass) s)
        .collect(Collectors.toList());
  }

  /**
   * Calculates all superClass symbols of the symbol recursively.
   *
   * @param symbol the symbol for which all super class symbols should be calculated.
   * @return List of all superClass symbols.
   */
  public static List<TypeSymbol> getAllSuperClasses(TypeSymbol symbol) {
    List<TypeSymbol> superClasses = new LinkedList<>();

    List<TypeSymbol> directSuperClasses =
        symbol.getSuperClassesOnly().stream()
            .map(SymTypeExpression::getTypeInfo)
            .collect(Collectors.toList());

    for (TypeSymbol superClass : directSuperClasses) {
      superClasses.addAll(getAllSuperClasses(superClass));
    }
    superClasses.addAll(directSuperClasses);

    return superClasses;
  }

  /**
   * returns the direct superclass of the given class if there is one.
   *
   * @param clazz the class for which the superclass is needed.
   * @return Optional of the superclass if it exists, otherwise Optional.empty()
   */
  public static Optional<ASTCDClass> getDirectSuperClass(ASTCDClass clazz) {
    if (!clazz.getSymbol().isPresentSuperClass())
      return Optional.empty();

    ASTNode superClass = clazz.getSymbol().getSuperClass().getTypeInfo().getAstNode();
    if (superClass instanceof ASTCDClass) {
      return Optional.of((ASTCDClass) superClass);
    }
    return Optional.empty();
  }

  /**
   * Resolves the class to a given roleSymbol in the enclosing Scope.
   * Note that the ASTNode must be attached to the ClassSymbol,
   * this might not be the case when classes are imported from other CDs (see getAllSuperClasses).
   *
   * @param roleSymbol the symbol to resolve.
   * @return the corresponding ASTCDClass if it is found otherwise Optional.empty()
   */
  public static Optional<ASTCDClass> resolveClass(CDRoleSymbol roleSymbol) {
    Optional<TypeSymbol> typeSymbol = roleSymbol.getEnclosingScope()
        .resolveType(roleSymbol.getType().getTypeInfo().getName());
    if (typeSymbol.isPresent() && typeSymbol.get().isPresentAstNode() && typeSymbol.get().getAstNode() instanceof ASTCDClass) {
      return Optional.of((ASTCDClass) typeSymbol.get().getAstNode());
    }
    return Optional.empty();
  }

  /**
   * Resolves the class to a given attribute in the enclosing Scope.
   * Note that the ASTNode must be attached to the ClassSymbol,
   * this might not be the case when classes are imported from other CDs (see getAllSuperClasses).
   *
   * @param attribute the symbol to resolve.
   * @return the corresponding ASTCDClass if it is found otherwise Optional.empty()
   */
  public static Optional<ASTCDClass> resolveClass(ASTCDAttribute attribute) {
    Optional<TypeSymbol> typeSymbol = attribute.getEnclosingScope()
        .resolveType(attribute.getSymbol().getType().getTypeInfo().getName());
    if (typeSymbol.isPresent() && typeSymbol.get().isPresentAstNode() && typeSymbol.get().getAstNode() instanceof ASTCDClass) {
      return Optional.of((ASTCDClass) typeSymbol.get().getAstNode());
    }
    return Optional.empty();
  }

  /**
   * Resolves the enum to a given roleSymbol in the enclosing Scope.
   * Note that the ASTNode must be attached to the EnumSymbol,
   * this might not be the case when enums are imported from other CDs (see getAllSuperClasses).
   *
   * @param roleSymbol the symbol to resolve.
   * @return the corresponding ASTCDEnum if it is found otherwise Optional.empty()
   */
  public static Optional<ASTCDEnum> resolveEnum(CDRoleSymbol roleSymbol) {
    Optional<TypeSymbol> typeSymbol = roleSymbol.getEnclosingScope()
        .resolveType(roleSymbol.getType().getTypeInfo().getName());
    if (typeSymbol.isPresent() && typeSymbol.get().isPresentAstNode() && typeSymbol.get().getAstNode() instanceof ASTCDEnum) {
      return Optional.of((ASTCDEnum) typeSymbol.get().getAstNode());
    }
    return Optional.empty();
  }

  /**
   * Resolves the enum to a given attribute in the enclosing Scope.
   * Note that the ASTNode must be attached to the EnumSymbol,
   * this might not be the case when enums are imported from other CDs (see getAllSuperClasses).
   *
   * @param attribute the symbol to resolve.
   * @return the corresponding ASTCDEnum if it is found otherwise Optional.empty()
   */
  public static Optional<ASTCDEnum> resolveEnum(ASTCDAttribute attribute) {
    Optional<TypeSymbol> typeSymbol = attribute.getEnclosingScope()
        .resolveType(attribute.getSymbol().getType().getTypeInfo().getName());
    if (typeSymbol.isPresent() && typeSymbol.get().isPresentAstNode() && typeSymbol.get().getAstNode() instanceof ASTCDEnum) {
      return Optional.of((ASTCDEnum) typeSymbol.get().getAstNode());
    }
    return Optional.empty();
  }

  /**
   * Generates the subclass Tree(s) of the given classes. A class that has no superclasses is the root of a unique subclass Tree.
   * This method generates all of them.
   *
   * @param classes the classes that should be part of the tree
   * @return a List of all root nodes of the trees
   */
  public static List<CD2GUIClassTreeNode> buildSubclassTrees(List<ASTCDClass> classes) {
    List<CD2GUIClassTreeNode> classTrees = new LinkedList<>();
    List<ASTCDClass> classesCopy = new LinkedList<>(classes);
    for (ASTCDClass clazz : classesCopy) {
      if (getDirectSuperClass(clazz).isEmpty()) { //class is root
        classTrees.add(new CD2GUIClassTreeNode(clazz));
        classes.remove(clazz);
      }
    }
    while (!classes.isEmpty()) {
      int listSize = classes.size();
      classesCopy = new LinkedList<>(classes);
      for (ASTCDClass clazz : classesCopy) {
        ASTCDClass superclass = getDirectSuperClass(clazz).orElseThrow();
        CD2GUIClassTreeNode parentNode = findNode(classTrees, superclass);
        if (parentNode != null) {
          parentNode.addSubclass(new CD2GUIClassTreeNode(clazz));
          classes.remove(clazz);
        }
      }
      if (listSize == classes.size()) {
        throw new IllegalArgumentException("Classes cannot form a coherent subclassTree");
      }
    }
    return classTrees;
  }

  /**
   * Finds the node of the argument in the list of Subclass Trees.
   *
   * @param clazz     The class that the node is needed for.
   * @param classTree The list of subclass tree roots
   * @return The node of the class.
   * @throws IllegalArgumentException if the node is not found in the trees
   */
  public static CD2GUIClassTreeNode getTreeNode(List<CD2GUIClassTreeNode> classTree, ASTCDClass clazz) {
    for (CD2GUIClassTreeNode node : classTree) {
      CD2GUIClassTreeNode clazzNode = node.getClassNode(clazz);
      if (clazzNode != null) {
        return clazzNode;
      }
    }
    throw new IllegalArgumentException("Class " + clazz.getName() + " not contained in the Tree");
  }

  private static CD2GUIClassTreeNode findNode(List<CD2GUIClassTreeNode> classTrees, ASTCDClass clazz) {
    for (CD2GUIClassTreeNode root : classTrees) {
      CD2GUIClassTreeNode node = root.getClassNode(clazz);
      if (node != null) {
        return node;
      }
    }
    return null;
  }


  /**
   * Checks if the class is marked as invisible in the CD.
   * This can be done by adding the Stereotype <<invisible>> to the class.
   * In this case the class won't show up in cd2gui.
   * returns true if the class is NOT invisible.
   *
   * @param clazz The class to check.
   * @return Whether the class is visible.
   */
  public static boolean isVisible(ASTCDClass clazz) {
    return !StereotypeManager.isInvisible(clazz);
  }

  /**
   * Checks if the enum is marked as invisible in the CD.
   * This can be done by adding the Stereotype <<invisible>> to the class.
   * In this case the class won't show up in cd2gui.
   * returns true if the class is NOT invisible.
   *
   * @param astcdEnum The enum to check.
   * @return Whether the enum is visible.
   */
  public static boolean isVisible(ASTCDEnum astcdEnum) {
    return !StereotypeManager.isInvisible(astcdEnum);
  }

  /**
   * returns whether the class has cd2gui pages, invisible classes and abstract classes don't have pages.
   *
   * @param clazz The class to check.
   * @return Whether the class has cd2gui pages.
   */
  public static boolean hasPage(ASTCDClass clazz) {
    return isVisible(clazz) && !clazz.getModifier().isAbstract() && !clazz.getModifier().isDerived();
  }

  /**
   * Checks if the class is marked as singleton in the CD.
   * This can be done by adding the Stereotype <<singleton>> to the class.
   *
   * @param clazz The class to check.
   * @return true if the class is singleton.
   */
  public static boolean isSingleton(ASTCDClass clazz) {
    return StereotypeManager.isSingleton(clazz);
  }
}
