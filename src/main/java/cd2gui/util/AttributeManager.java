/* (c) https://github.com/MontiCore/monticore */
package cd2gui.util;

import cd2gui.CD2GUITool;
import de.monticore.cdassociation._symboltable.CDRoleSymbol;
import de.monticore.cdbasis._ast.ASTCDAttribute;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.symbols.basicsymbols._symboltable.TypeSymbol;
import de.monticore.symbols.basicsymbols._symboltable.TypeSymbolSurrogate;
import de.monticore.symbols.oosymbols._symboltable.OOTypeSymbol;
import de.monticore.symbols.oosymbols._symboltable.OOTypeSymbolSurrogate;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class AttributeManager {


  /**
   * Calculates the all attributes of a class including the attributes of the superclass.
   *
   * @param clazz Class for which all attributes should be calculated
   * @return Set of all attributes including the attributes of the super classes
   */
  public static Set<ASTCDAttribute> getAllAttributes(ASTCDClass clazz, boolean filterInvisibleSuperClasses) {
    Set<ASTCDAttribute> attributes = new HashSet<>();
    for (ASTCDClass superClass : ASTCDClassManager.getAllSuperClasses(clazz)) {
      if (filterInvisibleSuperClasses && !ASTCDClassManager.isVisible(superClass))
        continue;
      attributes.addAll(superClass.getCDAttributeList());
    }
    attributes.addAll(clazz.getCDAttributeList());

    return attributes;
  }

  /**
   * Filters the set of attributes to keep only Lists.
   * @param attributes attributes to be filtered.
   * @return set with only the list attributes.
   */
  public static Set<ASTCDAttribute> filterAllListAttributes(Set<ASTCDAttribute> attributes){
    return attributes.stream().filter(a -> Types.isList(a.getSymbol().getType())).collect(Collectors.toSet());
  }

  /**
   * returns all attributes from the class associated with the role.
   * If the role is an Enum an Empty Set is returned, because enums cannot have attributes in the grammar.
   * @param role the role to get the attributes.
   * @return a set of all attributes of the role's class, if the role is not a class the empty Set
   */
  public static Set<ASTCDAttribute> getAssociationAttributes(CDRoleSymbol role, boolean filterInvisibleSuperClasses){

    return ASTCDClassManager.resolveClass(role)
            .map(astcdClass -> getAllAttributes(astcdClass, filterInvisibleSuperClasses))
            .orElse(Collections.emptySet());
  }

  /**
   * Checks whether the attribute is an enum. An enum is an OOType where IsEnum == true
   * @param attribute the attribute to check.
   * @return whether the attribute is an enum.
   */
  public static boolean isEnumAttribute(ASTCDAttribute attribute) {
    TypeSymbol typeInfo = attribute.getSymbol().getType().getTypeInfo();

    if (typeInfo instanceof TypeSymbolSurrogate) {
      typeInfo = ((TypeSymbolSurrogate) typeInfo).lazyLoadDelegate(); //use the method so save time, lazyLoadDelegate only calculates the type once and then saves it.
    }
    else if (typeInfo instanceof OOTypeSymbolSurrogate) {
      typeInfo = ((OOTypeSymbolSurrogate) typeInfo).lazyLoadDelegate();
    }

    if(typeInfo instanceof OOTypeSymbol)
      return ((OOTypeSymbol) typeInfo)
              .isIsEnum();

    return false;
  }

  /**
   * Checks whether the attribute is a class. An enum is an OOType where IsClass == true
   * @param attribute the attribute to check.
   * @return whether the attribute is a class.
   */
  public static boolean isClassAttribute(ASTCDAttribute attribute) {
    TypeSymbol typeInfo = attribute.getSymbol().getType().getTypeInfo();

    if (typeInfo instanceof TypeSymbolSurrogate) {
      typeInfo = ((TypeSymbolSurrogate) typeInfo).lazyLoadDelegate(); //use the method so save time, lazyLoadDelegate only calculates the type once and then saves it.
    }
    else if (typeInfo instanceof OOTypeSymbolSurrogate) {
      typeInfo = ((OOTypeSymbolSurrogate) typeInfo).lazyLoadDelegate();
    }

    if(typeInfo instanceof OOTypeSymbol)
      return ((OOTypeSymbol) typeInfo).isIsClass();

    return false;
  }

  public static boolean isDerived(ASTCDAttribute attribute){
    return attribute.getModifier().isDerived();
  }

  /**
   * Checks if the attribute is marked as invisible in the CD.
   * This can be done by adding the Stereotype <<invisible>> to the attribute.
   * In this case the attribute won't show up in cd2gui.
   * returns true if the attribute is NOT invisible.
   * @param attribute the attribute to check.
   * @return whether the attribute visible.
   */
  public static boolean isVisibleAttribute(ASTCDAttribute attribute) {
    return !StereotypeManager.isInvisible(attribute);
  }

  /**
   * Checks if the attribute is marked as invisible in the overview page.
   * This can be done by adding the Stereotype <<hidden_in_overview>> to the attribute.
   * In this case the attribute won't show up in the overview page, invisible attributes won't show up either.
   * returns true if the attribute is visible in the overview i.e. neither hidden_in_overview nor invisible.
   * @param attribute the attribute to check.
   * @return whether the attribute visible in the overview page.
   */
  public static boolean isVisibleAttributeInOverview(ASTCDAttribute attribute){
    return !StereotypeManager.isHiddenInOverview(attribute) && isVisibleAttribute(attribute); //Invisible Attributes are also hidden in Overview
  }

  /**
   * Checks if the attribute is marked as invisible in the details page.
   * This can be done by adding the Stereotype <<hidden_in_details>> to the attribute.
   * In this case the attribute won't show up in the details page, invisible attributes won't show up either.
   * returns true if the attribute is visible in the details i.e. neither hidden_in_details nor invisible.
   * @param attribute the attribute to check.
   * @return whether the attribute visible in the details page.
   */
  public static boolean isVisibleAttributeInDetails(ASTCDAttribute attribute){
    return !StereotypeManager.isHiddenInDetails(attribute) && isVisibleAttribute(attribute); //Invisible Attributes are also hidden in Overview
  }

  /**
   * filters the input for visible attributes.
   * @param attributes the set of attributes to filter.
   * @return a set of only visible attributes.
   */
  public static Set<ASTCDAttribute> filterInvisibleAttributeList(Set<ASTCDAttribute> attributes) {
    return attributes.stream()
            .filter(AttributeManager::isVisibleAttribute)
            .collect(Collectors.toSet());
  }

  /**
   * Generates the attributes from the class and filers them, needed for the dashboard template
   * @param clazz class to get filtered attributes for.
   * @return a set of only visible attributes of the class.
   */
  public static Set<ASTCDAttribute> filterInvisibleAttributeList(ASTCDClass clazz) {
    return filterInvisibleAttributeList(getAllAttributes(clazz, true));
  }

  /**
   * filters the input for attributes visible in the overview page.
   * @param attributes the set of attributes to filter.
   * @return a set of only attributes visible in the overview page.
   */
  public static Set<ASTCDAttribute> filterHiddenInOverview(Set<ASTCDAttribute> attributes){
    return attributes.stream().filter(AttributeManager::isVisibleAttributeInOverview).collect(Collectors.toSet());
  }

  /**
   * filters the input for attributes visible in the details page.
   * @param attributes the set of attributes to filter.
   * @return a set of only attributes visible in the details page.
   */
  public static Set<ASTCDAttribute> filterHiddenInDetails(Set<ASTCDAttribute> attributes){
    return attributes.stream().filter(AttributeManager::isVisibleAttributeInDetails).collect(Collectors.toSet());
  }

  public static boolean isOption(String option){
    return CD2GUITool.isOption(option);
  }
}
