package cd2gui.metrics;

/**
 * 指标相关构造型的枚举
 */
public enum MetricStereotype {
    METRIC("metric"),
    CHART_TYPE("chart_type"),
    METRIC_SCALE("metric_scale"),
    METRIC_UNIT("metric_unit"),
    METRIC_HIDDEN("metric_hidden");

    private final String value;

    MetricStereotype(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static MetricStereotype fromString(String text) {
        for (MetricStereotype stereotype : MetricStereotype.values()) {
            if (stereotype.value.equalsIgnoreCase(text)) {
                return stereotype;
            }
        }
        return null;
    }
}