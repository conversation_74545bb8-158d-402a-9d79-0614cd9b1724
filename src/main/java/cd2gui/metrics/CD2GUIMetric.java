package cd2gui.metrics;

import cd2gui.metrics.MetricScale;
import java.util.Set;

/**
 * 封装指标的属性和行为的数据结构
 */
public class CD2GUIMetric {
    private String name;
    private String description;
    private MetricScale scale;
    private Class<?> valueType;
    private Set<String> chartTypes;
    private Set<String> visibilityContexts;
    private String calculationMethod;
    
    // 构造函数
    public CD2GUIMetric(String name, String description, MetricScale scale, 
                       Class<?> valueType, Set<String> chartTypes, 
                       Set<String> visibilityContexts, String calculationMethod) {
        this.name = name;
        this.description = description;
        this.scale = scale;
        this.valueType = valueType;
        this.chartTypes = chartTypes;
        this.visibilityContexts = visibilityContexts;
        this.calculationMethod = calculationMethod;
    }
    
    // Getter和Setter方法
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public MetricScale getScale() {
        return scale;
    }
    
    public void setScale(MetricScale scale) {
        this.scale = scale;
    }
    
    public Class<?> getValueType() {
        return valueType;
    }
    
    public void setValueType(Class<?> valueType) {
        this.valueType = valueType;
    }
    
    public Set<String> getChartTypes() {
        return chartTypes;
    }
    
    public void setChartTypes(Set<String> chartTypes) {
        this.chartTypes = chartTypes;
    }
    
    public Set<String> getVisibilityContexts() {
        return visibilityContexts;
    }
    
    public void setVisibilityContexts(Set<String> visibilityContexts) {
        this.visibilityContexts = visibilityContexts;
    }
    
    public String getCalculationMethod() {
        return calculationMethod;
    }
    
    public void setCalculationMethod(String calculationMethod) {
        this.calculationMethod = calculationMethod;
    }
    
    public boolean isVisibleIn(String context) {
        return visibilityContexts.contains(context);
    }
}