package cd2gui.metrics;

import cd2gui.metrics.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理指标操作的工具类
 */
public class MetricManager {
    private static final MetricManager INSTANCE = new MetricManager();
    
    private final Map<String, CD2GUIMetric> metrics = new HashMap<>();
    
    private MetricManager() {
        // 私有构造函数确保单例
    }
    
    public static MetricManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 注册新指标
     */
    public void registerMetric(CD2GUIMetric metric) {
        metrics.put(metric.getName(), metric);
    }
    
    /**
     * 获取指标
     */
    public CD2GUIMetric getMetric(String name) {
        return metrics.get(name);
    }
    
    /**
     * 获取所有指标
     */
    public List<CD2GUIMetric> getAllMetrics() {
        return List.copyOf(metrics.values());
    }
    
    /**
     * 按量表类型过滤指标
     */
    public List<CD2GUIMetric> getMetricsByScale(MetricScale scale) {
        return metrics.values().stream()
                .filter(metric -> metric.getScale() == scale)
                .collect(Collectors.toList());
    }
    
    /**
     * 按可见性上下文过滤指标
     */
    public List<CD2GUIMetric> getMetricsByVisibilityContext(String context) {
        return metrics.values().stream()
                .filter(metric -> metric.isVisibleIn(context))
                .collect(Collectors.toList());
    }
    
    /**
     * 按图表类型过滤指标
     */
    public List<CD2GUIMetric> getMetricsByChartType(String chartType) {
        return metrics.values().stream()
                .filter(metric -> metric.getChartTypes().contains(chartType))
                .collect(Collectors.toList());
    }
    
    /**
     * 清除所有指标
     */
    public void clearMetrics() {
        metrics.clear();
    }
}