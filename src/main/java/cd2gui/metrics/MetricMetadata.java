package cd2gui.metrics;

/**
 * 存储指标元数据的结构
 */
public class MetricMetadata {
    private String description;
    private String unit;
    private String source;
    private String version;
    private String lastUpdated;
    private String owner;
    private String additionalInfo;

    // 构造函数
    public MetricMetadata(String description, String unit, String source) {
        this.description = description;
        this.unit = unit;
        this.source = source;
    }

    // Getter和Setter方法
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(String lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
}