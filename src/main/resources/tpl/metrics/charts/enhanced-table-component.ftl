<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

${name?uncap_first}_${attributeMetric.attributeName}Table@GemTable(
  width = "100%",
  height = "auto",
  layout = "auto",
  headers = [
    @GemTableHeader(cols = ["Property", "Value"])
  ],
  body = [
    <#-- Attribute name row -->
    @GemTableRow(cols = [
      @GemText(value = "Attribute Name"),
      @GemText(value = "${attributeMetric.attributeName}")
    ]),
    
    <#-- Data type row -->
    <#if attributeMetric.dataType??>
    @GemTableRow(cols = [
      @GemText(value = "Data Type"),
      @GemText(value = "${attributeMetric.dataType.displayName}")
    ]),
    </#if>
    
    <#-- Metric scale row -->
    <#if attributeMetric.scale??>
    @GemTableRow(cols = [
      @GemText(value = "Metric Scale"),
      @GemText(value = "${attributeMetric.scale.displayName}")
    ]),
    </#if>
    
    <#-- Confidence row -->
    <#if attributeMetric.confidence??>
    @GemTableRow(cols = [
      @GemText(value = "Confidence"),
      @GemText(value = "${attributeMetric.confidence?string('0.00')}")
    ]),
    </#if>
    
    <#-- Additional metadata rows -->
    <#if attributeMetric.metadata?? && attributeMetric.metadata?has_content>
      <#list attributeMetric.metadata?keys as key>
        <#if key != "value" && key != "unit" && key != "color" && key != "trend">
    @GemTableRow(cols = [
      @GemText(value = "${key?cap_first}"),
      @GemText(value = "${attributeMetric.metadata[key]!''}")
    ])<#sep>,</#sep>
        </#if>
      </#list>
    </#if>
  ]
);
