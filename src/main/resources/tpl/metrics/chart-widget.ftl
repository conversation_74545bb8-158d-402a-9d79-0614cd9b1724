<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#assign chartContent>
<#if attributeMetric.visualizationHint?? && attributeMetric.visualizationHint.recommendedChart??>
  <#assign chartType = attributeMetric.visualizationHint.recommendedChart.name()>

  <#-- Route to appropriate chart component based on recommended chart type -->
  <#switch chartType>
    <#case "PIE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.pie-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.innerRadius!50
      ])}
      <#break>
      
    <#case "BAR_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bar-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.width!"100%", 
        attributeMetric.visualizationHint.templateData.height!"200px"
      ])}
      <#break>
      
    <#case "LINE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.line-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.width!"100%", 
        attributeMetric.visualizationHint.templateData.height!"200px"
      ])}
      <#break>
      
    <#case "GAUGE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.gauge-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.min!0, 
        attributeMetric.visualizationHint.templateData.max!100, 
        attributeMetric.visualizationHint.templateData.unit!"", 
        attributeMetric.visualizationHint.templateData.textValue!""
      ])}
      <#break>
      
    <#case "BULLET_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bullet-chart", [
        attributeMetric.visualizationHint.templateData.value!0, 
        attributeMetric.visualizationHint.templateData.reference!0, 
        attributeMetric.visualizationHint.templateData.thresholdValue!0, 
        attributeMetric.visualizationHint.templateData.title!"", 
        attributeMetric.visualizationHint.templateData.subtitle!"", 
        attributeMetric.visualizationHint.templateData.range![], 
        attributeMetric.visualizationHint.templateData.steps![]
      ])}
      <#break>
      
    <#case "CANDLESTICK_CHART">
      ${tc.includeArgs("tpl.metrics.charts.candlestick-chart", [
        attributeMetric.visualizationHint.templateData.data![]
      ])}
      <#break>

    <#case "HEATMAP_CHART">
      ${tc.includeArgs("tpl.metrics.charts.heatmap-chart", [
        attributeMetric.visualizationHint.templateData.data!{},
        attributeMetric.visualizationHint.templateData.xLabel!"",
        attributeMetric.visualizationHint.templateData.yLabel!"",
        attributeMetric.visualizationHint.templateData.timestampdata!false
      ])}
      <#break>

    <#case "SUNBURST_CHART">
      ${tc.includeArgs("tpl.metrics.charts.sunburst-chart", [
        attributeMetric.visualizationHint.templateData.data!{},
        attributeMetric.visualizationHint.templateData.dataShort!{},
        attributeMetric.visualizationHint.templateData.colors![],
        attributeMetric.visualizationHint.templateData.ignoreError!true
      ])}
      <#break>

    <#case "SCATTER_PLOT">
      ${tc.includeArgs("tpl.metrics.charts.scatter-plot", [
        attributeMetric.visualizationHint.templateData.data!{},
        attributeMetric.visualizationHint.templateData.xAxis!{},
        attributeMetric.visualizationHint.templateData.yAxis!{}
      ])}
      <#break>

    <#case "DATA_TABLE">
      ${tc.includeArgs("tpl.metrics.charts.enhanced-table-component", [domainClass, name, attributeMetric])}
      <#break>

    <#default>
      ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
  </#switch>
<#else>
  <#-- No visualization hint available - use text display -->
  ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
</#if>
</#assign>





${name?uncap_first}_${attributeMetric.attributeName}ChartCard@GemCard(
  width = "45%",
  height = "220px",
  title = "${attributeMetric.attributeName} Analysis",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      ${chartContent}
      <#if attributeMetric.visualizationHint?? && attributeMetric.visualizationHint.description??>,
      @GemText(
        value = "${attributeMetric.visualizationHint.description}",
      )
      </#if>
    ]
  )
);
