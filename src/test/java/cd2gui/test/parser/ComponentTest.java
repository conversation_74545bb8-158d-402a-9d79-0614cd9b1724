/* (c) https://github.com/MontiCore/monticore */
package cd2gui.test.parser;

import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;

public class ComponentTest extends AbstractTest {

  @Test
  public void testDomain() throws IOException {
    generateGUI("src/test/resources/Domain.cd", TARGET_PATH);
    parserTest(TARGET_PATH);
  }

  @Test
  public void testSehub() throws IOException {
    generateGUI("src/test/resources/Sehub.cd", TARGET_PATH);
    parserTest(TARGET_PATH);
  }
}
