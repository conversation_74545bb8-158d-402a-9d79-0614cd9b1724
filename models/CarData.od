/* (c) https://github.com/MontiCore/monticore */

package init;

import CarRental.*;

objectdiagram CarData {

  rental: RentalService {};

  link rental (rental) -> (asset) vw;

  vw: Car {
    lineup = "Käfer";
    manufacturer = "VW";
    status = RentableStatus.RENTED;
  };

  vwPricing: Pricing {
    dailyFee = 0.0;
  };

  link vw (rentableAsset) -> (pricing) vwPricing;

  link rental (rental) -> (asset) ford;

  ford : Car {
    lineup = "Mustang";
    manufacturer = "Ford";
  };

  fordPricing: Pricing {
    dailyFee = 120.0;
  };

  link ford (rentableAsset) -> (pricing) fordPricing;

  link rental (rental) -> (customer) jim;

  jim : Customer {
    name = "<PERSON>";
  };

  // TODO The link for a rental transaction. Uncomment it to verify the
  //      implementation of the transactionManagement association.
  // link transactionManagement rental (rental) -> (transaction) transactionOne;

  transactionOne : RentalTransaction {
    rentalDate = LocalDateTime.of(2024, Month.SEPTEMBER, 4, 16, 30);
  };

  link transactionOne (rentalTransaction) <-> (customer) jim;
  link transactionOne (rentalTransaction) <-> (asset) vw;

  // TODO exercise 2.3
}
