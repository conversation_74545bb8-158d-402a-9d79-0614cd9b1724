package gui;

import mc.fenix.basic.GemButton;
import mc.fenix.basic.GemImage;
import mc.fenix.basic.GemText;
import mc.fenix.arrange.GemColumn;

import gui.navigation.NavContainer;
import gui.CarSave;

// data class
import CarRental.Car;

page CarUpdate(Car car) {
  @CarSave(car = car,
    image = @GemColumn(width = "200px", components = [
      @GemImage(source = getImageUrl(car.manufacturer)),
      @GemText(value = "Manufacturer: " + car.manufacturer)
    ]),
    button = @GemButton(label = "To overview", leftClick = toOverview())
  );
}
