package gui;

import mc.fenix.basic.GemText;
import mc.fenix.basic.GemImage;
import mc.fenix.basic.GemButton;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemGrid;
import mc.fenix.arrange.GemCard;
import mc.fenix.charts.GemLineChart;
import mc.fenix.charts.GemBarChart;
import mc.fenix.charts.GemPieChart;

import gui.navigation.NavContainer;

// data class
import CarRental.Car;
import CarRental.RentalTransaction;

page Dashboard(List<Car> cars, List<RentalTransaction> transactions) {
  @NavContainer(content = [
    @GemRow(wrap = "wrap", rowGap = "10px", hAlign = "space-evenly", components = [

        @GemCard(
          width = "40%",
          title = "Rented car count per month",
          component = @GemLineChart(data = getRentedCarCount(transactions))
        ),

        @GemCard(
          width = "40%",
          title = "Car rent count comparison",
          component = @GemBarChart(data = getRentCountPerManufacturer(cars))
        ),

        @GemCard(
          height = "400px",
          title = "Availability",
          component = @GemPieChart(data = getAvailability(cars))
        )

    ])
  ]);
}
