<!DOCTYPE html>
<html>
<head>
<title>可视化模板开发总结.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E5%8F%AF%E8%A7%86%E5%8C%96%E6%A8%A1%E6%9D%BF">可视化模板</h1>
<h2 id="%E8%B0%83%E7%94%A8%E5%85%B3%E7%B3%BB">调用关系</h2>
<pre class="hljs"><code><div>overview-gui.ftl
    ↓ (直接调用)
visualization-panel.ftl
    ↓ (内部判断是否有可视化数据)
    ├── 有数据 → chart-widget.ftl (直接输出完整图表卡片)
    │                ↓ (根据数据类型选择适当的图表)
    │                └── charts/.ftl (具体图表实现)
    └── 无数据 → 显示提示信息
</div></code></pre>
<h2 id="%E6%89%A9%E5%B1%95-overview-guiftl">扩展: overview-gui.ftl</h2>
<ol>
<li>
<p>GUI Models中增加图表相关的import</p>
<pre class="hljs"><code><div>${tc.includeArgs(<span class="hljs-string">"tpl.metrics.imports-metrics"</span>, [domainClass, name, classMetrics])}
</div></code></pre>
</li>
<li>
<p>直接调用<code>visualization-panel.ftl</code>
传递参数：</p>
<ul>
<li>domainClass</li>
<li>name</li>
<li>classMetrics</li>
</ul>
<pre class="hljs"><code><div>${tc.includeArgs(<span class="hljs-string">"tpl.metrics.visualization-panel"</span>, [domainClass, name, classMetrics])}
</div></code></pre>
</li>
</ol>
<h2 id="%E6%96%B0%E5%A2%9Emetrics">新增：metrics/</h2>
<h3 id="imports-metricsftl">imports-metrics.ftl</h3>
<p>导入所有必要的GUI和图表组件</p>
<h3 id="visualization-panelftl">visualization-panel.ftl</h3>
<h5 id="%E5%8A%9F%E8%83%BD">功能</h5>
<p>把统计概览、按属性粒度的图表网格及空状态提示封装在同一 @GemCard 内，供 Overview 页面按需 include。通过 <code>chart-widget</code> 把单个属性的度量图解耦出去，调用侧只需准备 classMetrics 数据对象即可完成整页指标可视化的拼装。</p>
<h5 id="%E5%86%85%E9%83%A8%E7%BB%93%E6%9E%84">内部结构</h5>
<ol>
<li>
<p>Statistics Summary
条件 <code>classMetrics.overallStats??</code></p>
<ul>
<li>满足：
<ul>
<li>总属性数 <code>totalAttributes</code></li>
<li>可视化属性数 <code>visualizableAttributesCount</code></li>
<li>平均置信度 <code>averageConfidence</code> (若存在)</li>
</ul>
</li>
</ul>
</li>
<li>
<p>Metrics visualization
条件 <code>classMetrics.hasVisualizableAttributes()</code></p>
<ul>
<li>满足：循环 <code>classMetrics.visualizableAttributes as attributeMetric</code> 并对每项调用chart-widget.ftl<pre class="hljs"><code><div>${tc.includeArgs(<span class="hljs-string">"tpl.metrics.chart-widget"</span>, [domainClass, name,attributeMetric])}
</div></code></pre>
</li>
<li>不满足：提示NoVisualizableMetrics</li>
</ul>
</li>
</ol>
<h5 id="%E5%AF%B9%E5%A4%96%E6%8E%A5%E5%8F%A3--%E4%BE%9D%E8%B5%96">对外接口 / 依赖</h5>
<p>classMetrics 提供：</p>
<ul>
<li>totalAttributes (int)</li>
<li>visualizableAttributesCount (int)</li>
<li>overallStats.averageConfidence (Double?)</li>
<li>hasVisualizableAttributes() (boolean)</li>
<li>visualizableAttributes (Iterable<AttributeMetric>) ⚠️</li>
</ul>
<h3 id="chart-widgetftl">chart-widget.ftl</h3>
<h5 id="%E5%8A%9F%E8%83%BD">功能</h5>
<p>根据attributeMetric自带的建议的图表类型(hint)动态选择正确的图表模板include，并将结果包进统一尺寸的@GemCard里，从而让上层组件一行代码即可获得“属性名 + 指定图表 + 描述”完整指标Card。</p>
<h5 id="%E5%86%85%E9%83%A8%E7%BB%93%E6%9E%84">内部结构</h5>
<ol>
<li>
<p>图表路由器
检查是否有所建议的图表类型。
条件<code>attributeMetric.visualizationHint.recommendedChart??</code></p>
<ul>
<li>满足：
<ul>
<li>从 <code>attributeMetric.visualizationHint.recommendedChart.name()</code> 获取 chartType ⚠️</li>
<li>用 FreeMarker &lt;#switch&gt; 语句路由到位于charts/目录下的具体图表模板</li>
</ul>
</li>
<li>不满足/switch default：按text-display方法显示。</li>
</ul>
</li>
<li>
<p>输出完整的GemCard包装的图表组件</p>
</li>
</ol>
<h5 id="%E5%AF%B9%E5%A4%96%E6%8E%A5%E5%8F%A3--%E4%BE%9D%E8%B5%96">对外接口 / 依赖</h5>
<p>attributeMetric提供：</p>
<ul>
<li>attributeName (string)</li>
<li>visualizationHint.recommendedChart (ChartType) ⚠️</li>
<li>visualizationHint.description (string)</li>
</ul>
<h3 id="chartsftl">charts/*.ftl</h3>
<p>11种图表。</p>
<p>直接调用现成的组件：</p>
<ul>
<li>bar</li>
<li>bullet</li>
<li>candlestick</li>
<li>gauge</li>
<li>heatmap</li>
<li>line</li>
<li>pie</li>
<li>scatter-plot</li>
<li>sunburst</li>
</ul>
<p>额外编写：</p>
<ul>
<li>
<p>enhanced-table-component.ftl
基于GemTable组件，显示内容：</p>
<table>
<thead>
<tr>
<th style="text-align:right">行序</th>
<th>条件</th>
<th>“Property” 列显示</th>
<th>“Value” 列显示</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:right">1</td>
<td>必显</td>
<td>Attribute Name</td>
<td><code>attributeMetric.attributeName</code></td>
<td>属性名称</td>
</tr>
<tr>
<td style="text-align:right">2</td>
<td>若 <code>dataType</code> 存在</td>
<td>Data Type</td>
<td><code>attributeMetric.dataType.displayName</code></td>
<td>数据类型</td>
</tr>
<tr>
<td style="text-align:right">3</td>
<td>若 <code>scale</code> 存在</td>
<td>Metric Scale</td>
<td><code>attributeMetric.scale.displayName</code></td>
<td>度量尺度</td>
</tr>
<tr>
<td style="text-align:right">4</td>
<td>若 <code>confidence</code> 存在</td>
<td>Confidence</td>
<td><code>attributeMetric.confidence</code></td>
<td>置信度</td>
</tr>
<tr>
<td style="text-align:right">5+</td>
<td>遍历 <code>metadata</code> 其他键</td>
<td><code>key</code></td>
<td><code>attributeMetric.metadata[key]</code></td>
<td>额外元数据</td>
</tr>
</tbody>
</table>
</li>
<li>
<p>text-display-component.ftl</p>
<ul>
<li>
<p>属性名称</p>
<ul>
<li><strong>始终显示</strong>：<code>${attributeMetric.attributeName}</code></li>
</ul>
</li>
<li>
<p>数据类型（可选）</p>
<ul>
<li><strong>条件</strong>：<code>attributeMetric.dataType</code> 存在</li>
<li><strong>文本</strong>：<code>Type: ${attributeMetric.dataType.displayName}</code></li>
</ul>
</li>
<li>
<p>度量尺度（可选）</p>
<ul>
<li><strong>条件</strong>：<code>attributeMetric.scale</code> 存在</li>
<li><strong>文本</strong>：<code>Scale: ${attributeMetric.scale.displayName}</code></li>
</ul>
</li>
<li>
<p>置信度（可选）</p>
<ul>
<li><strong>条件</strong>：<code>attributeMetric.confidence</code> 存在</li>
<li><strong>文本</strong>：<code>Confidence: ${attributeMetric.confidence?string('0.00')}</code></li>
</ul>
</li>
<li>
<p>状态提示（始终显示）</p>
<ul>
<li><strong>条件</strong>：
若 <code>visualizationHint</code> 存在 → “No suitable chart type available”<br>
否则 → “No visualization hint available”</li>
</ul>
</li>
<li>
<p>额外元数据（可选）</p>
<ul>
<li><strong>条件</strong>：<code>attributeMetric.metadata.value</code> 存在</li>
<li><strong>文本</strong>：<code>Value: ${attributeMetric.metadata.value} ${attributeMetric.metadata.unit!''}</code></li>
</ul>
</li>
</ul>
</li>
</ul>

</body>
</html>
