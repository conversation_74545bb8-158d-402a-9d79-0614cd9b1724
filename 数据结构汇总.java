public class ChartDetail<D, C> {
    private ChartType type;
    private ChartComponent<D, C> component;
    // …其他字段…

    public ChartDetail(ChartType type, ChartComponent<D, C> comp) {
        this.type = type;
        this.component = comp;
    }

    public ChartComponent<D, C> getComponent() {
        return component;
    }
}

/**
 * 一个通用的图表组件接口，
 * D：表示具体的数据类型（比如 GemBarChartData）
 * C：表示具体的配置类型（比如 GemBarChartConfig 或者一个 config POJO）
 */
public interface ChartComponent<D, C> {

    /** 输入整个图表的数据 */
    void setData(D data);

    /** 获取整个图表的数据 */
    D getData();

    /** 输入配置 */
    void setConfig(C config);

    /** 获取配置 */
    C getConfig();
}



// BarChart
import java.util.List;
import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import mc.fenix.charts.gembarcharttypes.GemBarChartDataBuilder;
import mc.fenix.charts.gembarcharttypes.GemBarChartEntry;
import mc.fenix.charts.gembarcharttypes.GemBarChartEntryBuilder;

public class BarChartConfig {
    private boolean stacked;
    private int     maxValue;
    private int     minValue;

    public BarChartConfig(boolean stacked, int maxValue, int minValue) {
        this.stacked  = stacked;
        this.maxValue = maxValue;
        this.minValue = minValue;
    }

    public boolean isStacked()    { return stacked; }
    public int     getMaxValue()  { return maxValue; }
    public int     getMinValue()  { return minValue; }
}

public class BarChartComponent implements ChartComponent<GemBarChartData, BarChartConfig> {
    private GemBarChartData barChartData;
    private BarChartConfig config;

    public void addEntry(String label, List<Integer> data){
        GemBarChartEntry entry  = new GemBarChartEntryBuilder()
                                        .label(label)
                                        .data(data)
                                        .build().get();
        barChartData.addEntries$mc_fenix_charts_gembarcharttypes_GemBarChartEntry(entry);
    }

    public void addLabels(String label){
        barChartData.addLabels$java_lang_String(label);
    }

    @Override
    public void setData(GemBarChartData data) {
        this.barChartData = data;
    }

    @Override
    public GemBarChartData getData() { return barChartData; }

    @Override
    public void setConfig(BarChartConfig config) { this.config = config; }

    @Override
    public BarChartConfig getConfig() { return config; }

}

//LineChart
import java.util.List;
import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import mc.fenix.charts.gemlinecharttypes.GemLineChartDataBuilder;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntry;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntryBuilder;

public class LineChartConfig {
    private boolean enableBackgroundColor;
    private int     maxValue;
    private int     minValue;

    public LineChartConfig(boolean enableBackgroundColor, int maxValue, int minValue) {
        this.enableBackgroundColor = enableBackgroundColor;
        this.maxValue = maxValue;
        this.minValue = minValue;
    }

    public boolean isBackgroundColorEnabled()    { return enableBackgroundColor; }
    public int     getMaxValue()  { return maxValue; }
    public int     getMinValue()  { return minValue; }
}

public class LineChartComponent implements ChartComponent<GemLineChartData, LineChartConfig> {
    private GemLineChartData lineChartData;
    private LineChartConfig config;

    public void addEntry(String label, List<Integer> data){
        GemLineChartEntry entry  = new GemLineChartEntryBuilder()
                .label(label)
                .data(data)
                .build().get();
        lineChartData.addEntries$mc_fenix_charts_gemlinecharttypes_GemLineChartEntry(entry);
    }

    public void addLabels(String label){
        lineChartData.addLabels$java_lang_String(label);
    }

    @Override
    public void setData(GemLineChartData data) {
        this.lineChartData = data;
    }

    @Override
    public GemLineChartData getData() { return lineChartData; }

    @Override
    public void setConfig(LineChartConfig config) { this.config = config; }

    @Override
    public LineChartConfig getConfig() { return config; }
}


//PieChart
import mc.fenix.charts.gempiecharttypes.GemPieChartData;
import mc.fenix.charts.gempiecharttypes.GemPieChartDataBuilder;
import mc.fenix.charts.gempiecharttypes.GemPieChartEntry;
import mc.fenix.charts.gempiecharttypes.GemPieChartEntryBuilder;

public class PieChartConfig {
    private int innerRadius;

    public PieChartConfig(int innerRadius) {
        this.innerRadius = innerRadius;
    }

    public int getInnerRadius()  { return innerRadius; }
}

public class PieChartComponent implements ChartComponent<GemPieChartData, PieChartConfig> {
    private GemPieChartData pieChartData;
    private PieChartConfig config;

    public void addEntry(String label, int data){
        GemPieChartEntry entry  = new GemPieChartEntryBuilder()
                .label(label)
                .data(data)
                .build().get();
        pieChartData.addEntries$mc_fenix_charts_gempiecharttypes_GemPieChartEntry(entry);
    }

    @Override
    public void setData(GemPieChartData data) {
        this.pieChartData = data;
    }

    @Override
    public GemPieChartData getData() { return pieChartData; }

    @Override
    public void setConfig(PieChartConfig config) { this.config = config; }

    @Override
    public PieChartConfig getConfig() { return config; }
}