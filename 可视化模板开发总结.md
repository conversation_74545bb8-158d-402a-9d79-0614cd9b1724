# 可视化模板

## 调用关系

```java
overview-gui.ftl
    ↓ (直接调用)
visualization-panel.ftl
    ↓ (内部判断是否有可视化数据)
    ├── 有数据 → chart-widget.ftl (直接输出完整图表卡片)
    │                ↓ (根据数据类型选择适当的图表)
    │                └── charts/.ftl (具体图表实现)
    └── 无数据 → 显示提示信息
```

## 扩展: overview-gui.ftl

1. GUI Models中增加图表相关的import
   ```java
   ${tc.includeArgs("tpl.metrics.imports-metrics", [domainClass, name, classMetrics])}
   ```

2. 直接调用`visualization-panel.ftl`
   传递参数：
   - domainClass
   - name
   - classMetrics
   ```java
   ${tc.includeArgs("tpl.metrics.visualization-panel", [domainClass, name, classMetrics])}
   ```

## 新增：metrics/

### imports-metrics.ftl

导入所有必要的GUI和图表组件


### visualization-panel.ftl

##### 功能
把统计概览、按属性粒度的图表网格及空状态提示封装在同一 @GemCard 内，供 Overview 页面按需 include。通过 `chart-widget` 把单个属性的度量图解耦出去，调用侧只需准备 classMetrics 数据对象即可完成整页指标可视化的拼装。

##### 内部结构

1. Statistics Summary
条件 `classMetrics.overallStats??`
   - 满足：
     + 总属性数 `totalAttributes`
     + 可视化属性数 `visualizableAttributesCount`
     + 平均置信度 `averageConfidence` (若存在)
  
2. Metrics visualization
条件 `classMetrics.hasVisualizableAttributes()`
   - 满足：循环 `classMetrics.visualizableAttributes as attributeMetric` 并对每项调用chart-widget.ftl
      ```java
      ${tc.includeArgs("tpl.metrics.chart-widget", [domainClass, name,attributeMetric])}
      ```
   - 不满足：提示NoVisualizableMetrics

##### 对外接口 / 依赖

classMetrics 提供：
- totalAttributes (int)
- visualizableAttributesCount (int)
- overallStats.averageConfidence (Double?)
- hasVisualizableAttributes() (boolean)
- visualizableAttributes (Iterable<AttributeMetric>) ⚠️

### chart-widget.ftl

##### 功能
根据attributeMetric自带的建议的图表类型(hint)动态选择正确的图表模板include，并将结果包进统一尺寸的@GemCard里，从而让上层组件一行代码即可获得“属性名 + 指定图表 + 描述”完整指标Card。

##### 内部结构

1. 图表路由器
检查是否有所建议的图表类型。
条件`attributeMetric.visualizationHint.recommendedChart??`
   - 满足：
      + 从 `attributeMetric.visualizationHint.recommendedChart.name()` 获取 chartType ⚠️
      + 用 FreeMarker <#switch> 语句路由到位于charts/目录下的具体图表模板
   - 不满足/switch default：按text-display方法显示。

2. 输出完整的GemCard包装的图表组件

##### 对外接口 / 依赖

attributeMetric提供：
- attributeName (string)
- visualizationHint.recommendedChart (ChartType) ⚠️
- visualizationHint.description (string)


### charts/*.ftl

11种图表。

直接调用现成的组件：
- bar
- bullet
- candlestick
- gauge
- heatmap
- line
- pie
- scatter-plot
- sunburst

额外编写：
- enhanced-table-component.ftl
   基于GemTable组件，显示内容：
  | 行序 | 条件                     | “Property” 列显示            | “Value” 列显示                                   | 说明           |
   |-----:|--------------------------|------------------------------|--------------------------------------------------|----------------|
   | 1    | 必显                     | Attribute Name               | `attributeMetric.attributeName`                  | 属性名称       |
   | 2    | 若 `dataType` 存在       | Data Type                    | `attributeMetric.dataType.displayName`           | 数据类型       |
   | 3    | 若 `scale` 存在          | Metric Scale                 | `attributeMetric.scale.displayName`              | 度量尺度       |
   | 4    | 若 `confidence` 存在     | Confidence                   | `attributeMetric.confidence`     | 置信度         |
   | 5+   | 遍历 `metadata` 其他键   | `key`         | `attributeMetric.metadata[key]`                  | 额外元数据     |

- text-display-component.ftl
   - 属性名称  
      - **始终显示**：`${attributeMetric.attributeName}`

  - 数据类型（可选）  
    - **条件**：`attributeMetric.dataType` 存在  
    - **文本**：`Type: ${attributeMetric.dataType.displayName}`

  - 度量尺度（可选）  
    - **条件**：`attributeMetric.scale` 存在  
    - **文本**：`Scale: ${attributeMetric.scale.displayName}`

  - 置信度（可选）  
    - **条件**：`attributeMetric.confidence` 存在  
    - **文本**：`Confidence: ${attributeMetric.confidence?string('0.00')}`

  - 状态提示（始终显示）  
    - **条件**：
      若 `visualizationHint` 存在 → “No suitable chart type available”  
      否则 → “No visualization hint available”

  - 额外元数据（可选）  
    - **条件**：`attributeMetric.metadata.value` 存在  
    - **文本**：`Value: ${attributeMetric.metadata.value} ${attributeMetric.metadata.unit!''}`


